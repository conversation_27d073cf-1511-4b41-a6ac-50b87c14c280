<?php

namespace App\Mail;

use App\Models\LottieFile;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ShareLottieAnimation extends Mailable
{
    use Queueable, SerializesModels;

    public $lottieFile;
    public $shareUrl;
    public $senderMessage;

    /**
     * Create a new message instance.
     */
    public function __construct(LottieFile $lottieFile, string $shareUrl, string $senderMessage = "")
    {
        $this->lottieFile = $lottieFile;
        $this->shareUrl = $shareUrl;
        $this->senderMessage = $senderMessage ?: "Check out this Lottie animation I wanted to share with you!";
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Shared Lottie Animation: ' . $this->lottieFile->original_name,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.share-lottie',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
