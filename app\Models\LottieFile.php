<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;

class LottieFile extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'uuid',
        'original_name',
        'file_name',
        'file_path',
        'mime_type',
        'file_size',
        'metadata',
        'is_valid',
        'status',
        'user_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'metadata' => 'array',
        'is_valid' => 'boolean',
        'file_size' => 'integer',
    ];

    /**
     * The "booted" method of the model.
     */
    protected static function booted(): void
    {
        static::creating(function ($lottieFile) {
            $lottieFile->uuid = $lottieFile->uuid ?? Str::uuid();
        });
    }

    /**
     * Get the user that owns the lottie file.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the full URL to the lottie file.
     */
    public function getUrlAttribute(): string
    {
        return url(Storage::url($this->file_path));
    }

    /**
     * Get the file content.
     */
    public function getContent(): string
    {
        return Storage::get($this->file_path);
    }

    /**
     * Scope a query to only include active files.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include valid files.
     */
    public function scopeValid($query)
    {
        return $query->where('is_valid', true);
    }
}
