function K(s,c,u){navigator.clipboard&&window.isSecureContext?navigator.clipboard.writeText(s).then(()=>{c&&c()}).catch(d=>{console.error("Clipboard API failed:",d),b(s,c,u)}):b(s,c,u)}function b(s,c,u){const d=document.createElement("textarea");d.value=s,d.style.top="0",d.style.left="0",d.style.position="fixed",d.style.opacity="0",document.body.appendChild(d),d.focus(),d.select();try{const h=document.execCommand("copy");h&&c?c():!h&&u&&u()}catch(h){console.error("Fallback copy failed:",h),u&&u()}document.body.removeChild(d)}document.addEventListener("clipboard-copy",s=>{const c=s.detail;navigator.clipboard&&window.isSecureContext?navigator.clipboard.writeText(c).then(()=>{document.dispatchEvent(new CustomEvent("clipboard-success"))}).catch(()=>{b(c)}):b(c)});document.addEventListener("DOMContentLoaded",function(){const s=document.getElementById("drop-area"),c=document.getElementById("browse-button"),u=document.getElementById("lottie_file"),d=document.getElementById("preview-container"),h=document.getElementById("close-preview"),F=document.getElementById("file-name-display"),o=document.getElementById("lottiePlayer"),R=document.getElementById("lottie-container"),U=document.getElementById("play"),j=document.getElementById("pause"),W=document.getElementById("stop"),B=document.querySelectorAll(".speed-btn"),S=document.getElementById("background"),I=document.getElementById("loop"),k=document.getElementById("autoplay"),M=document.getElementById("download-json"),N=document.getElementById("server-upload-form");let p=null,m=null,g={};["dragenter","dragover","dragleave","drop"].forEach(e=>{s.addEventListener(e,O,!1),document.body.addEventListener(e,O,!1)}),["dragenter","dragover"].forEach(e=>{s.addEventListener(e,P,!1),document.body.addEventListener(e,P,!1)}),["dragleave","drop"].forEach(e=>{s.addEventListener(e,T,!1),document.body.addEventListener(e,T,!1)}),s.addEventListener("drop",_,!1),c.addEventListener("click",()=>{u.click()}),u.addEventListener("change",()=>{u.files&&u.files[0]&&J(u.files)}),h.addEventListener("click",()=>{d.classList.add("hidden"),s.classList.remove("hidden"),o.load(""),document.getElementById("server-upload-container").classList.remove("hidden"),document.getElementById("share-email-container").classList.add("hidden"),p=null}),U.addEventListener("click",()=>{o.play()}),j.addEventListener("click",()=>{o.pause()}),W.addEventListener("click",()=>{o.stop()}),B.forEach(e=>{e.addEventListener("click",()=>{const t=parseFloat(e.getAttribute("data-speed"));typeof o.setSpeed=="function"?o.setSpeed(t):o.speed=t,B.forEach(n=>{n.classList.remove("bg-gray-700","text-white"),n.classList.add("bg-gray-200","text-gray-700")}),e.classList.remove("bg-gray-200","text-gray-700"),e.classList.add("bg-gray-700","text-white")})}),S.addEventListener("input",()=>{R.style.backgroundColor=S.value}),I.addEventListener("change",()=>{I.checked?o.setAttribute("loop",""):o.removeAttribute("loop")}),k.addEventListener("change",()=>{k.checked?(o.setAttribute("autoplay",""),o.play()):(o.removeAttribute("autoplay"),o.pause())}),M.addEventListener("click",()=>{if(m){const e=document.createElement("a");e.href=URL.createObjectURL(new Blob([JSON.stringify(m,null,2)],{type:"application/json"})),e.download=p?p.name:"animation.json",document.body.appendChild(e),e.click(),document.body.removeChild(e)}else if(p){const e=document.createElement("a");e.href=URL.createObjectURL(p),e.download=p.name,document.body.appendChild(e),e.click(),document.body.removeChild(e)}});let w=null;N&&N.addEventListener("submit",e=>{if(e.preventDefault(),document.getElementById("share-btn-spinner").style.display="inline-block",p){if(!p.type.includes("json")&&!p.name.endsWith(".json")){Swal.fire({icon:"error",title:"Validation Error",text:"Please upload a valid JSON file"}),document.getElementById("share-btn-spinner").style.display="none";return}const t=new FormData;t.append("lottie_file",p),t.append("_token",window.csrfToken),fetch("/lottie/upload",{method:"POST",body:t,headers:{"X-Requested-With":"XMLHttpRequest"}}).then(async n=>{const r=await n.text();if(!n.ok)throw new Error(`Server responded with ${n.status}: ${r}`);try{return JSON.parse(r)}catch{throw new Error("Invalid JSON response from server")}}).then(n=>{n.success?(f=n.share_url,w=n.uuid,document.getElementById("server-upload-container").classList.add("hidden"),document.getElementById("share-email-container").classList.remove("hidden")):Swal.fire({icon:"error",title:"Upload Error",text:n.message||"Error uploading file"})}).catch(n=>{console.error("Upload Error:",n),Swal.fire({icon:"error",title:"Upload Error",text:"Error uploading file: "+n.message})}).finally(()=>{document.getElementById("share-btn-spinner").style.display="none"})}});const v=document.getElementById("share-button"),A=document.getElementById("share-email");A.classList.add("text-black");let f=null;v.onclick=()=>{const e=A.value.trim();if(!e){Swal.fire({icon:"error",title:"Validation Error",text:"Please enter an email address"});return}if(!f){Swal.fire({icon:"error",title:"Validation Error",text:"No animation to share. Please upload and save an animation first."});return}v.disabled=!0,v.textContent="Sending...",fetch(`/lottie/${w}/share-email`,{method:"POST",headers:{"Content-Type":"application/json","X-CSRF-TOKEN":window.csrfToken},body:JSON.stringify({email:e,uuid:w,share_url:f,message:"Check out this awesome Lottie animation!"})}).then(t=>t.json()).then(t=>{if(t.success){document.getElementById("share-email-container").classList.add("hidden"),document.getElementById("share-link-container").classList.remove("hidden");const a=document.getElementById("copy-link"),i=document.getElementById("share-link");i.value=f,i.classList.add("text-black"),a.addEventListener("click",()=>{K(f,()=>{console.log("Link copied to clipboard!",f);const l=a.textContent;a.textContent="Copied!",a.classList.add("bg-green-600"),setTimeout(()=>{a.textContent=l,a.classList.remove("bg-green-600")},2e3)},()=>{Swal.fire({icon:"error",title:"Copy Failed",text:"Failed to copy link. Please copy manually: "+f})})})}else Swal.fire({icon:"error",title:"Share Error",text:t.message||"Error sharing animation"})}).catch(t=>{Swal.fire({icon:"error",title:"Share Error",text:"Error sharing animation: "+t.message}),console.error("Error:",t)}).finally(()=>{v.disabled=!1,v.textContent="Share"})};function O(e){e.preventDefault(),e.stopPropagation()}function P(e){e.preventDefault(),e.stopPropagation(),s.classList.add("border-primary","bg-blue-50")}function T(e){e.preventDefault(),e.stopPropagation(),s.classList.remove("border-primary","bg-blue-50")}function _(e){e.preventDefault(),e.stopPropagation();const n=e.dataTransfer.files;J(n)}function J(e){if(e.length>0){const t=e[0];if(t.type==="application/json"||t.name.endsWith(".json")){p=t,F.textContent=t.name;const n=new FileReader;n.onload=function(r){try{m=JSON.parse(r.target.result),g={},C(m),$(),H(),s.classList.add("hidden"),d.classList.remove("hidden")}catch(a){Swal.fire({icon:"error",title:"JSON Error",text:"Invalid JSON file. Please upload a valid Lottie JSON file."}),console.error("Error parsing JSON:",a)}},n.readAsText(t)}else Swal.fire({icon:"error",title:"File Error",text:"Please upload a JSON file."})}}function C(e,t=[]){if(Array.isArray(e))e.forEach((n,r)=>C(n,t.concat(r)));else if(typeof e=="object"&&e!==null)for(const n in e)if(n==="c"&&e[n]&&Array.isArray(e[n].k)){const r=e[n].k;if(Array.isArray(r)&&r.length>=3){const a=r.slice(0,3).map(l=>Math.round(l*255));if(a.some(l=>!Number.isFinite(l)||l<0||l>255))continue;const i="#"+a.map(l=>l.toString(16).padStart(2,"0")).join("");g[i]||(g[i]=[]),g[i].push({path:t.concat([n,"k"]),value:r})}}else C(e[n],t.concat(n))}function V(e,t,n){let r=e;for(let a=0;a<t.length-1;a++)r=r[t[a]];r[t[t.length-1]]=n}function $(){const e=document.getElementById("color-swatches");e.innerHTML="";const t=Object.keys(g);for(let n=0;n<t.length;n+=2){const r=document.createElement("div");r.className="flex items-center gap-2";const a=t[n];if(a==="#NaNNaNNaN")continue;const i=document.createElement("div");i.className="flex items-center gap-2",i.style.minWidth="7rem",i.style.maxWidth="7rem";const l=D(a),L=document.createElement("span");if(L.textContent=a,L.style.color="#000",i.appendChild(l),i.appendChild(L),r.appendChild(i),n+1<t.length){const E=t[n+1];if(E==="#NaNNaNNaN")continue;const y=document.createElement("div");y.className="flex items-center gap-2",y.style.marginLeft="4rem";const X=D(E),x=document.createElement("span");x.textContent=E,x.style.color="#000",y.appendChild(X),y.appendChild(x),r.appendChild(y)}e.appendChild(r)}}function D(e){if(typeof e!="string"||!/^#[0-9a-f]{6}$/i.test(e))return console.warn("Invalid color hex:",e),document.createElement("div");const t=document.createElement("input");return t.type="color",t.value=e,t.style.width="32px",t.style.height="32px",t.dataset.hex=e,t.addEventListener("input",n=>{const r=n.target.value,a=parseInt(r.slice(1,3),16)/255,i=parseInt(r.slice(3,5),16)/255,l=parseInt(r.slice(5,7),16)/255;g[e].forEach(({path:L,value:E})=>{const y=E[3]!==void 0?E[3]:1;V(m,L,[a,i,l,y])}),q()}),t}function q(){const e=o.currentTime,t=o.isPaused,n=JSON.stringify(m);o.load(URL.createObjectURL(new Blob([n],{type:"application/json"}))),o.addEventListener("ready",function r(){o.removeEventListener("ready",r),typeof e=="number"&&!isNaN(e)&&o.seek(e),t||o.play()},{once:!0})}function H(){const e=o.isPaused,t=o.currentFrame,n=o.hasAttribute("loop"),r=o.hasAttribute("autoplay"),a=new Blob([JSON.stringify(m)],{type:"application/json"}),i=URL.createObjectURL(a);function l(){o.removeEventListener("ready",l),typeof o.setSpeed=="function"?o.setSpeed(1):o.speed=1,typeof t=="number"&&!isNaN(t)&&o.seek(t),e?o.pause():o.play(),n?o.setAttribute("loop",""):o.removeAttribute("loop"),r?o.setAttribute("autoplay",""):o.removeAttribute("autoplay")}o.addEventListener("ready",l),o.load(i)}});
