/* LottieFiles-inspired Theme */

:root {
    --primary: #6366f1;
    --primary-hover: #4f46e5;
    --secondary: #f97316;
    --secondary-hover: #ea580c;
    --accent: #8b5cf6;
    --accent-hover: #7c3aed;
    --success: #10b981;
    --warning: #f59e0b;
    --danger: #ef4444;
    --info: #3b82f6;
    --dark: #1e293b;
    --light: #f8fafc;
    --background: #ffffff;
    --text: #0f172a;
    --text-muted: #64748b;
    --border: #e2e8f0;
    --card-bg: #ffffff;
    --card-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --header-height: 70px;
    --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--secondary) 0%, var(--warning) 100%);
}

/* Typography */
body {
    font-family: 'Inter', sans-serif;
    color: var(--text);
    background-color: var(--background);
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
}

h1 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
}

h2 {
    font-size: 2rem;
    margin-bottom: 1.25rem;
}

h3 {
    font-size: 1.75rem;
    margin-bottom: 1rem;
}

/* Buttons */
.btn-primary {
    background: var(--primary);
    color: white;
    border: none;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: var(--primary-hover);
    transform: translateY(-2px);
}

.btn-secondary {
    background: var(--secondary);
    color: white;
    border: none;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: var(--secondary-hover);
    transform: translateY(-2px);
}

.btn-gradient {
    background: var(--gradient-primary);
    color: white;
    border: none;
    transition: all 0.3s ease;
}

.btn-gradient:hover {
    opacity: 0.9;
    transform: translateY(-2px);
}

/* Cards */
.card {
    background: var(--card-bg);
    border-radius: 0.75rem;
    box-shadow: var(--card-shadow);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

/* Header */
.header {
    height: var(--header-height);
    background-color: var(--background);
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 1.5rem;
}

.logo {
    font-weight: 800;
    font-size: 1.5rem;
    color: var(--primary);
}

/* Hero Section */
.hero {
    background: var(--gradient-primary);
    color: white;
    padding: 6rem 0;
    position: relative;
    overflow: hidden;
}

.hero-content {
    position: relative;
    z-index: 1;
}

.hero h1 {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
}

.hero p {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* Features */
.features {
    padding: 5rem 0;
}

.feature-card {
    padding: 2rem;
    text-align: center;
}

.feature-icon {
    width: 4rem;
    height: 4rem;
    margin: 0 auto 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gradient-primary);
    border-radius: 50%;
    color: white;
}

/* Footer */
.footer {
    background-color: var(--dark);
    color: white;
    padding: 4rem 0 2rem;
}

.footer-links h4 {
    font-size: 1.25rem;
    margin-bottom: 1.5rem;
    color: white;
}

.footer-links ul {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 0.75rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: white;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 2rem;
    margin-top: 3rem;
    text-align: center;
    color: rgba(255, 255, 255, 0.5);
}

/* Animations */
.animate-fade-in {
    animation: fadeIn 1s ease-in-out;
}

.animate-slide-up {
    animation: slideUp 0.8s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}
