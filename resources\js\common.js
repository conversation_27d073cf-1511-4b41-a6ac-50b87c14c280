// Clipboard functionality
function copyToClipboard(text, successCallback, errorCallback) {
    // Modern browsers with HTTPS
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(text).then(() => {
            if (successCallback) successCallback();
        }).catch((err) => {
            console.error('Clipboard API failed:', err);
            fallbackCopyTextToClipboard(text, successCallback, errorCallback);
        });
    } else {
        // Fallback for older browsers or non-HTTPS
        fallbackCopyTextToClipboard(text, successCallback, errorCallback);
    }
}

function fallbackCopyTextToClipboard(text, successCallback, errorCallback) {
    const textArea = document.createElement("textarea");
    textArea.value = text;

    // Avoid scrolling to bottom
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    textArea.style.opacity = "0";

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        if (successful && successCallback) {
            successCallback();
        } else if (!successful && errorCallback) {
            errorCallback();
        }
    } catch (err) {
        console.error('Fallback copy failed:', err);
        if (errorCallback) errorCallback();
    }

    document.body.removeChild(textArea);
}

document.addEventListener('clipboard-copy', event => {
    const text = event.detail;
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(text)
            .then(() => {
                document.dispatchEvent(new CustomEvent('clipboard-success'));
            })
            .catch(() => {
                fallbackCopyTextToClipboard(text);
            });
    } else {
        fallbackCopyTextToClipboard(text);
    }
});

document.addEventListener('DOMContentLoaded', function() {
    const dropArea = document.getElementById('drop-area');
    const browseButton = document.getElementById('browse-button');
    const fileInput = document.getElementById('lottie_file');
    const previewContainer = document.getElementById('preview-container');
    const closePreview = document.getElementById('close-preview');
    const fileNameDisplay = document.getElementById('file-name-display');
    const lottiePlayer = document.getElementById('lottiePlayer');
    const lottieContainer = document.getElementById('lottie-container');
    const playBtn = document.getElementById('play');
    const pauseBtn = document.getElementById('pause');
    const stopBtn = document.getElementById('stop');
    const speedBtns = document.querySelectorAll('.speed-btn');
    const bgColorControl = document.getElementById('background');
    const loopControl = document.getElementById('loop');
    const autoplayControl = document.getElementById('autoplay');
    const downloadJsonBtn = document.getElementById('download-json');
    const serverUploadForm = document.getElementById('server-upload-form');

    let currentFile = null;
    let lottieJsonData = null; // Store parsed JSON for color editing
    let colorMap = {}; // { colorString: [ { path, value } ] }

    // Prevent default drag behaviors
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, preventDefaults, false);
        document.body.addEventListener(eventName, preventDefaults, false);
    });

    // Highlight drop area when item is dragged over it
    ['dragenter', 'dragover'].forEach(eventName => {
        dropArea.addEventListener(eventName, highlight, false);
        document.body.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, unhighlight, false);
        document.body.addEventListener(eventName, unhighlight, false);
    });

    // Handle dropped files
    dropArea.addEventListener('drop', handleDrop, false);

    // Handle browse button
    browseButton.addEventListener('click', () => { 
        fileInput.click();
    });

    // Handle file selection
    fileInput.addEventListener('change', () => {
        if (fileInput.files && fileInput.files[0]) {
            handleFiles(fileInput.files);
        }
    });

    // Close preview
    closePreview.addEventListener('click', () => {
        previewContainer.classList.add('hidden');
        dropArea.classList.remove('hidden');
        lottiePlayer.load('');
        document.getElementById('server-upload-container').classList.remove('hidden');
        document.getElementById('share-email-container').classList.add('hidden');
        currentFile = null;
    });

    // Player controls
    playBtn.addEventListener('click', () => {
        lottiePlayer.play();
    });

    pauseBtn.addEventListener('click', () => {
        lottiePlayer.pause();
    });

    stopBtn.addEventListener('click', () => {
        lottiePlayer.stop();
    });

    // Speed controls
    speedBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const speed = parseFloat(btn.getAttribute('data-speed'));
            if (typeof lottiePlayer.setSpeed === 'function') {
                lottiePlayer.setSpeed(speed);
            } else {
                lottiePlayer.speed = speed; // fallback for custom implementations
            }

            // Update active button
            speedBtns.forEach(b => {
                b.classList.remove('bg-gray-700', 'text-white');
                b.classList.add('bg-gray-200', 'text-gray-700');
            });
            btn.classList.remove('bg-gray-200', 'text-gray-700');
            btn.classList.add('bg-gray-700', 'text-white');
        });
    });

    // Background color control
    bgColorControl.addEventListener('input', () => {
        lottieContainer.style.backgroundColor = bgColorControl.value;
    });

    // Loop control
    loopControl.addEventListener('change', () => {
        if (loopControl.checked) {
            lottiePlayer.setAttribute('loop', '');
        } else {
            lottiePlayer.removeAttribute('loop');
        }
    });

    // Autoplay control
    autoplayControl.addEventListener('change', () => {
        if (autoplayControl.checked) {
            lottiePlayer.setAttribute('autoplay', '');
            lottiePlayer.play();
        } else {
            lottiePlayer.removeAttribute('autoplay');
            lottiePlayer.pause();
        }
    });

    // Download JSON
    downloadJsonBtn.addEventListener('click', () => {
        if (lottieJsonData) {
            const a = document.createElement('a');
            a.href = URL.createObjectURL(new Blob([JSON.stringify(lottieJsonData, null, 2)], {
                type: 'application/json'
            }));
            a.download = currentFile ? currentFile.name : 'animation.json';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
        } else if (currentFile) {
            const a = document.createElement('a');
            a.href = URL.createObjectURL(currentFile);
            a.download = currentFile.name;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
        }
    });
    let uuid = null;
    // Server upload form
    if (serverUploadForm) {
        serverUploadForm.addEventListener('submit', (e) => {
            e.preventDefault();
            document.getElementById('share-btn-spinner').style.display = 'inline-block';

            if (currentFile) {
                // Validate file type
                if (!currentFile.type.includes('json') && !currentFile.name.endsWith('.json')) {
                    // alert('Please upload a valid JSON file');
                    Swal.fire({
                        icon: 'error',
                        title: 'Validation Error',
                        text: 'Please upload a valid JSON file'
                    });
                    document.getElementById('share-btn-spinner').style.display = 'none';
                    return;
                }

                const formData = new FormData();
                formData.append('lottie_file', currentFile);
                formData.append('_token', window.csrfToken);
                fetch('/lottie/upload', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(async response => {
                    const responseData = await response.text();
                    // Hide spinner moved to finally block
                    if (!response.ok) {
                        throw new Error(`Server responded with ${response.status}: ${responseData}`);
                    }
                    try {
                        return JSON.parse(responseData);
                    } catch (e) {
                        throw new Error('Invalid JSON response from server');
                    }
                })
                .then(data => {
                    if (data.success) {
                        // Store the share URL
                        currentShareUrl = data.share_url;
                        uuid = data.uuid;
                        document.getElementById('server-upload-container').classList.add('hidden');
                        document.getElementById('share-email-container').classList.remove('hidden');
        
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Upload Error',
                            text: data.message || 'Error uploading file'
                        });
                    }
                })
                .catch(error => {
                    console.error('Upload Error:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Upload Error',
                        text: 'Error uploading file: ' + error.message
                    });
                })
                .finally(() => {
                    document.getElementById('share-btn-spinner').style.display = 'none';
                });
            }
        });
    }
    const shareButton = document.getElementById('share-button');
    const shareEmailInput = document.getElementById('share-email');
    shareEmailInput.classList.add('text-black');
    let currentShareUrl = null; // Store the share URL

    // Update share button click handler
    shareButton.onclick = () => {
        const email = shareEmailInput.value.trim();

        if (!email) {
                Swal.fire({
                    icon: 'error',
                    title: 'Validation Error',
                    text: 'Please enter an email address'
                });
            return;
        }
        if (!currentShareUrl) {
                Swal.fire({
                    icon: 'error',
                    title: 'Validation Error',
                    text: 'No animation to share. Please upload and save an animation first.'
                });
            return;
        }
       

        // Disable button and show loading state
        shareButton.disabled = true;
        shareButton.textContent = 'Sending...';

        // Send email request
        fetch(`/lottie/${uuid}/share-email`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': window.csrfToken
            },
            body: JSON.stringify({
                email: email,
                uuid: uuid,
                share_url: currentShareUrl,
                message: 'Check out this awesome Lottie animation!'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // hide the share email container
                const shareEmailContainer = document.getElementById('share-email-container');
                shareEmailContainer.classList.add('hidden');
                const shareLinkContainer = document.getElementById('share-link-container');
                shareLinkContainer.classList.remove('hidden');
                // Create a copy link button
                const copyLinkBtn = document.getElementById('copy-link');
                const shareLinkInput = document.getElementById('share-link');
                shareLinkInput.value = currentShareUrl;
                shareLinkInput.classList.add('text-black');
                copyLinkBtn.addEventListener('click', () => {
                    copyToClipboard(currentShareUrl,
                        () => {
                            console.log('Link copied to clipboard!', currentShareUrl);
                            // Show visual feedback
                            const originalText = copyLinkBtn.textContent;
                            copyLinkBtn.textContent = 'Copied!';
                            copyLinkBtn.classList.add('bg-green-600');
                            setTimeout(() => {
                                copyLinkBtn.textContent = originalText;
                                copyLinkBtn.classList.remove('bg-green-600');
                            }, 2000);
                        },
                        () => {
                            Swal.fire({
                                icon: 'error',
                                title: 'Copy Failed',
                                text: 'Failed to copy link. Please copy manually: ' + currentShareUrl
                            });
                        }
                    );
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Share Error',
                    text: data.message || 'Error sharing animation'
                });
            }
        })
        .catch(error => {
            Swal.fire({
                icon: 'error',
                title: 'Share Error',
                text: 'Error sharing animation: ' + error.message
            }); 
            console.error('Error:', error);
        })
        .finally(() => {
            // Re-enable button and restore text
            shareButton.disabled = false;
            shareButton.textContent = 'Share';
        });
    };
    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    function highlight(e) {
        e.preventDefault();
        e.stopPropagation();
        dropArea.classList.add('border-primary', 'bg-blue-50');
    }

    function unhighlight(e) {
        e.preventDefault();
        e.stopPropagation();
        dropArea.classList.remove('border-primary', 'bg-blue-50');
    }

    function handleDrop(e) {
        e.preventDefault();
        e.stopPropagation();
        const dt = e.dataTransfer;
        const files = dt.files;
        handleFiles(files);
    }

    function handleFiles(files) {
        if (files.length > 0) {
            const file = files[0];
            if (file.type === 'application/json' || file.name.endsWith('.json')) {
                currentFile = file;
                fileNameDisplay.textContent = file.name;
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        lottieJsonData = JSON.parse(e.target.result);
                        colorMap = {};
                        extractColors(lottieJsonData);
                        renderColorEditor();
                        reloadLottieFromJson();
                        dropArea.classList.add('hidden');
                        previewContainer.classList.remove('hidden');
                    } catch (error) {
                        Swal.fire({
                            icon: 'error',
                            title: 'JSON Error',
                            text: 'Invalid JSON file. Please upload a valid Lottie JSON file.'
                        });
                        console.error('Error parsing JSON:', error);
                    }
                };
                reader.readAsText(file);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'File Error',
                    text: 'Please upload a JSON file.'
                });
            }
        }
    }

    function extractColors(obj, path = []) {
        // Recursively find all solid color arrays in the Lottie JSON
        if (Array.isArray(obj)) {
            obj.forEach((item, i) => extractColors(item, path.concat(i)));
        } else if (typeof obj === 'object' && obj !== null) {
            for (const key in obj) {
                if (key === 'c' && obj[key] && Array.isArray(obj[key].k)) {
                    // Solid color: { c: { k: [r,g,b,a] } }
                    const colorArr = obj[key].k;
                    if (Array.isArray(colorArr) && colorArr.length >= 3) {
                        // Validate all color values are finite numbers
                        const rgb = colorArr.slice(0, 3).map(x => Math.round(x * 255));
                        if (rgb.some(x => !Number.isFinite(x) || x < 0 || x > 255)) {
                            continue; // Skip invalid color
                        }
                        const hex = '#' + rgb.map(x => x.toString(16).padStart(2, '0')).join('');
                        if (!colorMap[hex]) colorMap[hex] = [];
                        colorMap[hex].push({
                            path: path.concat([key, 'k']),
                            value: colorArr
                        });
                    }
                } else {
                    extractColors(obj[key], path.concat(key));
                }
            }
        }
    }

    function getValueByPath(obj, path) {
        return path.reduce((acc, key) => acc && acc[key], obj);
    }

    function setValueByPath(obj, path, value) {
        let ref = obj;
        for (let i = 0; i < path.length - 1; i++) ref = ref[path[i]];
        ref[path[path.length - 1]] = value;
    }

    function renderColorEditor() {
        const swatches = document.getElementById('color-swatches');
        swatches.innerHTML = '';
        const hexColors = Object.keys(colorMap);

        // Process colors in pairs
        for (let i = 0; i < hexColors.length; i += 2) {
            const div = document.createElement('div');
            div.className = 'flex items-center gap-2';

            // First color
            const hex1 = hexColors[i];
            if (hex1 === '#NaNNaNNaN') continue;
            const colorDiv1 = document.createElement('div');
            colorDiv1.className = 'flex items-center gap-2';
            colorDiv1.style.minWidth = '7rem';
            colorDiv1.style.maxWidth = '7rem';
            const input1 = createColorInput(hex1);
            const label1 = document.createElement('span');
            label1.textContent = hex1;
            label1.style.color = '#000'; // Make the label color black('text-black'); 
            colorDiv1.appendChild(input1);
            colorDiv1.appendChild(label1);
            div.appendChild(colorDiv1);

            // Second color (if exists)
            if (i + 1 < hexColors.length) {
                const hex2 = hexColors[i + 1];
                if (hex2 === '#NaNNaNNaN') continue;
                const colorDiv2 = document.createElement('div');
                colorDiv2.className = 'flex items-center gap-2';
                colorDiv2.style.marginLeft = '4rem';
                const input2 = createColorInput(hex2);
                const label2 = document.createElement('span');
                label2.textContent = hex2;
                // Make the label color black
                label2.style.color = '#000';
                colorDiv2.appendChild(input2);
                colorDiv2.appendChild(label2);
                div.appendChild(colorDiv2);
            }

            swatches.appendChild(div);
        }
    }

    function createColorInput(hex) {
        if (typeof hex !== 'string' || !/^#[0-9a-f]{6}$/i.test(hex)) {
            console.warn('Invalid color hex:', hex);
            return document.createElement('div'); // or skip rendering
        }

        const input = document.createElement('input');
        input.type = 'color';
        input.value = hex;
        input.style.width = '32px';
        input.style.height = '32px';
        input.dataset.hex = hex;

        input.addEventListener('input', (e) => {
            const newHex = e.target.value;
            const r = parseInt(newHex.slice(1, 3), 16) / 255;
            const g = parseInt(newHex.slice(3, 5), 16) / 255;
            const b = parseInt(newHex.slice(5, 7), 16) / 255;

            // Update the color in the JSON data
            colorMap[hex].forEach(({
                path,
                value
            }) => {
                const a = value[3] !== undefined ? value[3] : 1;
                setValueByPath(lottieJsonData, path, [r, g, b, a]);
            });

            // Use a more efficient update method
            updateLottieWithNewColors();
        });

        return input;
    }

    function updateLottieWithNewColors() {
        // Get current state
        const currentTime = lottiePlayer.currentTime;
        const isPaused = lottiePlayer.isPaused;

        // Create a new animation source with updated colors
        const updatedJsonString = JSON.stringify(lottieJsonData);

        // Update the animation source without reloading the player
        lottiePlayer.load(URL.createObjectURL(new Blob([updatedJsonString], {
            type: 'application/json'
        })));

        // Once loaded, restore state
        lottiePlayer.addEventListener('ready', function onReady() {
            lottiePlayer.removeEventListener('ready', onReady);

            // Restore time position if valid
            if (typeof currentTime === 'number' && !isNaN(currentTime)) {
                lottiePlayer.seek(currentTime);
            }

            // Restore play state
            if (!isPaused) {
                lottiePlayer.play();
            }
        }, {
            once: true
        });
    }

    function reloadLottieFromJson() {
        // Save playback state
        const isPaused = lottiePlayer.isPaused;
        const currentFrame = lottiePlayer.currentFrame;
        const isLooping = lottiePlayer.hasAttribute('loop');
        const isAutoplay = lottiePlayer.hasAttribute('autoplay');

        // Create a blob and reload the player
        const blob = new Blob([JSON.stringify(lottieJsonData)], {
            type: 'application/json'
        });
        const objectUrl = URL.createObjectURL(blob);

        // Listen for the animation to be loaded
        function onReady() {
            lottiePlayer.removeEventListener('ready', onReady);
            // Set default speed to 1x on first load
            if (typeof lottiePlayer.setSpeed === 'function') {
                lottiePlayer.setSpeed(1);
            } else {
                lottiePlayer.speed = 1;
            }
            // Restore the previous frame if valid
            if (typeof currentFrame === 'number' && !isNaN(currentFrame)) {
                lottiePlayer.seek(currentFrame);
            }
            if (!isPaused) {
                lottiePlayer.play();
            } else {
                lottiePlayer.pause();
            }
            // Restore loop and autoplay attributes if needed
            if (isLooping) lottiePlayer.setAttribute('loop', '');
            else lottiePlayer.removeAttribute('loop');
            if (isAutoplay) lottiePlayer.setAttribute('autoplay', '');
            else lottiePlayer.removeAttribute('autoplay');
        }
        lottiePlayer.addEventListener('ready', onReady);
        lottiePlayer.load(objectUrl);
    }
});