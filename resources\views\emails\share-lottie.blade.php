<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shared <PERSON><PERSON> Animation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #4F46E5;
            margin-bottom: 10px;
        }
        .animation-info {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .animation-info h3 {
            margin-top: 0;
            color: #4F46E5;
        }
        .btn {
            display: inline-block;
            background-color: #4F46E5;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            margin: 20px 0;
            font-weight: bold;
        }
        .btn:hover {
            background-color: #3730A3;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 14px;
        }
        .message {
            background-color: #e0f2fe;
            padding: 15px;
            border-left: 4px solid #4F46E5;
            margin: 20px 0;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 Lottie Animation Shared</h1>
            <p>Someone has shared a Lottie animation with you!</p>
        </div>

        @if($senderMessage)
        <div class="message">
            <strong>Message:</strong> {{ $senderMessage }}
        </div>
        @endif

        <div class="animation-info">
            <h3>Animation Details</h3>
            <p><strong>File Name:</strong> {{ $lottieFile->original_name }}</p>
            <p><strong>File Size:</strong> {{ number_format($lottieFile->file_size / 1024, 2) }} KB</p>
            @if($lottieFile->metadata)
                @if(isset($lottieFile->metadata['width']) && isset($lottieFile->metadata['height']))
                <p><strong>Dimensions:</strong> {{ $lottieFile->metadata['width'] }} x {{ $lottieFile->metadata['height'] }}</p>
                @endif
                @if(isset($lottieFile->metadata['frames']))
                <p><strong>Frame Rate:</strong> {{ $lottieFile->metadata['frames'] }} FPS</p>
                @endif
            @endif
            <p><strong>Uploaded:</strong> {{ $lottieFile->created_at->format('M d, Y \a\t g:i A') }}</p>
        </div>

        <div style="text-align: center;">
            <a href="{{ $shareUrl }}" class="btn">View Animation</a>
        </div>

        <p>Click the button above to view and interact with the Lottie animation. You can play, pause, adjust speed, and even modify colors!</p>

        <div class="footer">
            <p>This email was sent from {{ config('app.name') }}</p>
            <p>If you didn't expect this email, you can safely ignore it.</p>
        </div>
    </div>
</body>
</html>
