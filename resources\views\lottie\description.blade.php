<section class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 space-y-10 text-gray-800">
  <div class="space-y-4">
    <h2 class="text-2xl font-bold">Lottie Animations & Lottie Preview Tool</h2>
    <p>
      Lottie animations have transformed how designers and developers bring interactive, lightweight animations to life on web and mobile platforms. With the rise of <PERSON><PERSON>, the need for fast, intuitive tools to preview, test, and customize animations in real time has grown significantly.
    </p>
    <p>
      That’s where our tool, <strong>Lottie Preview by The Lottie Company</strong>, comes in. Whether you’re a developer testing animations for Android, or a designer creating effects animations in After Effects, this platform is designed to enhance your workflow.
    </p>
  </div>

  <div class="space-y-2">
    <h3 class="text-xl font-semibold">What is <PERSON><PERSON>?</h3>
    <p>
      Lottie is an open-source animation framework developed by Airbnb that renders After Effects animations in real-time on web and mobile devices. It uses JSON-based files exported via the Bodymovin extension, making complex animations lightweight and easily scalable.
    </p>
    <p>
      Since <PERSON><PERSON> was introduced, it has become a standard in UI animation for websites, apps, and digital interfaces. Designers and developers use <PERSON><PERSON> to create loading indicators, onboarding animations, icon transitions, and more.
    </p>
    <p>
      Unlike traditional video or GIF formats, Lottie files are vector-based, resolution-independent, and smaller in size, making them perfect for responsive, high-performance projects.
    </p>
  </div>

  <div class="space-y-2">
    <h3 class="text-xl font-semibold">What is a Lottie JSON File?</h3>
    <p>
      A Lottie JSON file is a specially structured JSON file that contains all the necessary information to render an animation — shapes, layers, timing, colors, transformations, and effects. These files are exported from After Effects using the Bodymovin extension.
    </p>
    <p>
      Developers and designers can preview Lottie JSON files, test animations, and implement them into their apps without needing to re-code or rasterize their visuals. These files support interactive animations and allow for real-time customization—an essential feature in modern design workflows.
    </p>
  </div>

  <div class="space-y-2">
    <h3 class="text-xl font-semibold">Why Use Lottie Animations in Modern Web/App Design?</h3>
    <ul class="list-disc list-inside space-y-1">
      <li><strong>Cross-platform support:</strong> Runs on iOS, Android, and all major web browsers.</li>
      <li><strong>Lightweight:</strong> Smaller file sizes than video or GIF.</li>
      <li><strong>Customizable:</strong> Change visuals without editing the original animation file.</li>
      <li><strong>Real-time rendering:</strong> Instant preview and feedback.</li>
      <li><strong>Enhanced UX:</strong> Boosts engagement with smooth, scalable animations.</li>
    </ul>
    <p>
      Designers and developers now rely heavily on Lottie animations for web, apps, and cross-device interfaces due to their flexibility and visual appeal.
    </p>
  </div>

  <div class="space-y-2">
    <h3 class="text-xl font-semibold">Overview of Lottie Preview at The Lottie Company</h3>
    <p>
      At The Lottie Company, we’ve built a powerful, free tool called <strong>Lottie Preview</strong> that allows you to preview, test, and customize Lottie animations directly in your browser.
    </p>
    <ul class="list-disc list-inside space-y-1">
      <li>Preview your Lottie animation instantly by uploading or pasting a JSON file</li>
      <li>Change colors, background settings, and zoom levels</li>
      <li>Use it as a Lottie editor to tweak visuals without opening After Effects</li>
      <li>Download the JSON file, test across devices, and prepare for deployment</li>
      <li>View animations in fullscreen and simulate mobile or desktop environments</li>
      <li>Upcoming features: export to MP4, GIF, and transparent GIF formats</li>
    </ul>
    <p>
      Our Lottie viewer is designed for speed, reliability, and intuitive use across iOS, Android, and web.
    </p>
  </div>

  <div class="space-y-2">
    <h3 class="text-xl font-semibold">Lottie Preview Tool Overview</h3>
    <p>
      In today’s fast-paced design and development world, being able to quickly preview, test, and iterate animations is essential. The Lottie Preview tool by The Lottie Company allows real-time Lottie animation previewing, editing, and sharing—all in the browser.
    </p>
    <p>
      Whether you’re an app designer, frontend developer, or motion animator, Lottie Preview makes it easy to preview and test Lottie JSON files directly.
    </p>
  </div>

  <div class="space-y-2">
    <h3 class="text-xl font-semibold">What is Lottie Preview?</h3>
    <p>
      Lottie Preview is a free, browser-based tool for instantly rendering Lottie animations. It functions as a lightweight player and editor with features like background customization, zooming, and JSON file export—no After Effects or native app required.
    </p>
    <ul class="list-disc list-inside space-y-1">
      <li>Drag-and-drop or upload a Lottie file</li>
      <li>Live preview for web, iOS, and Android</li>
      <li>Modify background and animation colors</li>
      <li>Test effects in real time</li>
      <li>Prepare animations for React Native, Flutter, etc.</li>
    </ul>
    <p>
      With a simple interface and robust functionality, Lottie Preview bridges the gap between design and implementation.
    </p>
  </div>

  <div class="space-y-2">
    <h3 class="text-xl font-semibold">Who is it for (Designers, Developers, Animators)?</h3>
    <p>Lottie Preview benefits everyone involved in modern digital animation:</p>
    <div class="pl-4 space-y-2">
      <p><strong>👩‍🎨 Designers</strong></p>
      <ul class="list-disc list-inside space-y-1">
        <li>Preview animations without writing code</li>
        <li>Adjust visuals to match themes</li>
        <li>Export updated files or share previews</li>
        <li>Test outside of After Effects</li>
      </ul>

      <p><strong>👨‍💻 Developers</strong></p>
      <ul class="list-disc list-inside space-y-1">
        <li>Test JSON files across environments</li>
        <li>Simulate iOS/Android performance</li>
        <li>Preview before embedding in apps</li>
        <li>Verify animations render as expected</li>
      </ul>

      <p><strong>🎞️ Animators</strong></p>
      <ul class="list-disc list-inside space-y-1">
        <li>Ensure Bodymovin-exported files render right</li>
        <li>Fine-tune without reopening source files</li>
        <li>Verify timing and effects in the browser</li>
        <li>Share previews with clients or teams</li>
      </ul>
    </div>
    <p>
      Whether you’re building for a splash screen or a full UI, Lottie Preview ensures everything works perfectly before launch.
    </p>
  </div>
<div class="py-12">

    <div>
        <h2 class="text-3xl font-bold mb-4">How Does Lottie Preview Work?</h2>
        <p class="mb-4">Using Lottie Preview is incredibly easy, and it works entirely in your browser—no installation or login required. Here’s a quick breakdown of the workflow:</p>
        <ul class="list-disc list-inside space-y-2">
            <li><strong>Upload or paste a Lottie JSON file:</strong> Drag and drop your .json file or paste the raw JSON code into the preview window.</li>
            <li><strong>Instantly preview your Lottie:</strong> The tool will render the animation in a responsive viewer that mimics different screen sizes and devices.</li>
        </ul>
        <p class="mt-4 font-semibold">Customize in real-time:</p>
        <ul class="list-disc list-inside space-y-2 ml-4">
            <li>Adjust background color</li>
            <li>Change animation colors using our built-in color editor</li>
            <li>Zoom in/out to focus on animation details</li>
            <li>Toggle full-screen mode for presentations or demo purposes</li>
        </ul>
        <p class="mt-4">Download or share: Once you’re satisfied, you can download the modified Lottie JSON file or save the animation state for testing across devices.</p>
        <p class="mt-4">The entire tool is optimized for speed and ease of use, allowing you to preview Lottie animations directly, test design assumptions, and integrate animations faster into your apps. Upcoming features like exporting to MP4, GIF, and transparent GIF will make it even more versatile for multi-platform deployment.</p>
    </div>

    <div>
        <h2 class="text-3xl font-bold mb-4">Lottie Preview Features</h2>
        <p class="mb-4">Lottie Preview by The Lottie Company is packed with intuitive features designed to help designers, developers, and animators easily preview, test, and share Lottie animations across all platforms.</p>

        <div class="space-y-6">
            <div>
                <h3 class="text-2xl font-semibold">Preview Lottie Animations for Free</h3>
                <p class="mt-2">Preview your Lottie animation instantly—no subscriptions, installation, or limitations.</p>
                <ul class="list-disc list-inside mt-2">
                    <li>Preview before using in a project</li>
                    <li>Test animation speed, timing, and transitions</li>
                    <li>Visualize effects animations without opening After Effects</li>
                </ul>
            </div>

            <div>
                <h3 class="text-2xl font-semibold">Edit Lottie Colors Live</h3>
                <ul class="list-disc list-inside mt-2">
                    <li>Adjust fill and stroke colors for any layer</li>
                    <li>Make visual updates without re-exporting</li>
                    <li>Preview updated animation instantly</li>
                </ul>
            </div>

            <div>
                <h3 class="text-2xl font-semibold">Change Background Colors of Animations</h3>
                <ul class="list-disc list-inside mt-2">
                    <li>Instantly change the preview background</li>
                    <li>Test visibility across themes</li>
                    <li>Preview animations in light/dark modes</li>
                </ul>
            </div>

            <div>
                <h3 class="text-2xl font-semibold">Zoom In and Out of Animations</h3>
                <ul class="list-disc list-inside mt-2">
                    <li>Inspect vector shapes and movements</li>
                    <li>Spot rendering issues or overlaps</li>
                    <li>Test responsive behavior</li>
                </ul>
            </div>

            <div>
                <h3 class="text-2xl font-semibold">View Lottie Animation in Full Screen</h3>
                <ul class="list-disc list-inside mt-2">
                    <li>Use for presentations or demos</li>
                    <li>Final review before integration</li>
                </ul>
            </div>

            <div>
                <h3 class="text-2xl font-semibold">Download Lottie JSON Files</h3>
                <ul class="list-disc list-inside mt-2">
                    <li>Download updated JSON after editing</li>
                    <li>Share with developers or upload to codebases</li>
                </ul>
            </div>

            <div>
                <h3 class="text-2xl font-semibold">Preview, Test and Share Lottie Animations</h3>
                <ul class="list-disc list-inside mt-2">
                    <li>Preview on different devices and screen sizes</li>
                    <li>Test browser/mobile rendering</li>
                    <li>Share via URL or embed</li>
                </ul>
            </div>

            <div>
                <h3 class="text-2xl font-semibold">Lottie Preview on Mac and iOS</h3>
                <ul class="list-disc list-inside mt-2">
                    <li>Upload and test from MacBook or iPhone</li>
                    <li>No app download required</li>
                    <li>Simulate various device resolutions</li>
                </ul>
            </div>
        </div>
    </div>

    <div>
        <h2 class="text-3xl font-bold mb-4">How to Use Lottie Preview</h2>
        <p class="mb-4">Using Lottie Preview is simple, fast, and doesn’t require any software installation.</p>

        <div class="space-y-6">
            <div>
                <h3 class="text-2xl font-semibold">Upload or Drag-and-Drop Lottie JSON</h3>
                <ul class="list-disc list-inside mt-2">
                    <li>Drag-and-drop or paste JSON into the interface</li>
                    <li>Instant in-browser rendering</li>
                    <li>Supports Bodymovin-exported files</li>
                </ul>
            </div>

            <div>
                <h3 class="text-2xl font-semibold">Edit and Customize Animation</h3>
                <ul class="list-disc list-inside mt-2">
                    <li>Change fill/stroke colors</li>
                    <li>Modify background</li>
                    <li>Zoom in/out, play/pause, scrub timeline</li>
                </ul>
                <p class="mt-2">Customize your Lottie visually and interactively for loaders, banners, or micro-interactions—no After Effects needed.</p>
            </div>
        </div>
    </div>
</div>
</section>
<section class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 space-y-10 text-gray-800">
    <div class="max-w-5xl mx-auto space-y-12">
        <div>
            <h2 class="text-2xl font-bold mb-4">Export or Download Options</h2>
            <p class="mb-4">Once you’re satisfied with your animation, Lottie Preview offers flexible download and export options:</p>
            <ul class="list-disc ml-6 space-y-1 mb-4">
                <li>Download the edited JSON file instantly with all changes saved</li>
                <li>Prepare the file for integration into projects using React Native, Swift, Flutter, or Web</li>
            </ul>
            <p class="mb-4">Upcoming features will allow exporting to:</p>
            <ul class="list-disc ml-6 space-y-1 mb-4">
                <li>MP4/WebM video</li>
                <li>GIF</li>
                <li>Transparent GIF for overlays and layered animations</li>
            </ul>
            <p>Until then, you can still download your updated Lottie JSON and implement it directly into your app or design system. This ensures a clean, production-ready file that’s been previewed, tested, and optimized.</p>
        </div>

        <div>
            <h2 class="text-2xl font-bold mb-4">Upcoming Features in Lottie Preview</h2>
            <p class="mb-4">At The Lottie Company, we’re constantly evolving our Lottie Preview tool...</p>
            <h3 class="text-xl font-semibold mb-2">Export as MP4 (WebM)</h3>
            <ul class="list-disc ml-6 space-y-1 mb-4">
                <li>Use Lottie animations in environments that don’t support Lottie JSON</li>
                <li>Convert Lottie to video content for social media, marketing, or motion presentations</li>
                <li>Maintain visual consistency by rendering animations as high-quality video output</li>
            </ul>
            <p class="mb-4">With the MP4/WebM export, you can:</p>
            <ul class="list-disc ml-6 space-y-1 mb-4">
                <li>Share animations on YouTube, Instagram, or product demos</li>
                <li>Incorporate animations into video apps and desktop software</li>
                <li>Test how Lotties look when used outside of JSON-based environments</li>
            </ul>

            <h3 class="text-xl font-semibold mb-2">Export as GIF (Standard and Transparent)</h3>
            <ul class="list-disc ml-6 space-y-1 mb-4">
                <li>Standard GIF: For use in emails, web banners, chats, or blogs</li>
                <li>Transparent GIF: Perfect for layering on websites and custom UI elements</li>
            </ul>
            <p class="mb-4">With just a few clicks, users will be able to:</p>
            <ul class="list-disc ml-6 space-y-1 mb-4">
                <li>Convert Lottie JSON into looping GIFs</li>
                <li>Preview the animation before download</li>
                <li>Export at custom sizes and frame rates</li>
            </ul>

            <h3 class="text-xl font-semibold mb-2">Plans for Adding Lottie to After Effects Integration</h3>
            <p class="mb-4">Planned improvements may include:</p>
            <ul class="list-disc ml-6 space-y-1 mb-4">
                <li>Direct import of After Effects files (.aep) into Lottie Preview via Bodymovin</li>
                <li>Live preview of animation changes from After Effects in our web tool</li>
                <li>Real-time sync of animation edits, reducing round-trip exporting</li>
            </ul>
            <p>By previewing and testing Lotties instantly from After Effects, teams will save time, reduce errors, and improve collaboration between animation and development.</p>
        </div>

        <div>
            <h2 class="text-2xl font-bold mb-4">Use Cases for Lottie Preview</h2>
            <p class="mb-4">Lottie Preview is an essential tool for testing, customizing, and sharing Lottie animations with ease.</p>
            <h3 class="text-xl font-semibold mb-2">Web and Mobile UI Animations</h3>
            <ul class="list-disc ml-6 space-y-1 mb-4">
                <li>Button interactions</li>
                <li>Micro-interactions (e.g. likes, taps, toggles)</li>
                <li>Menu transitions</li>
                <li>Tab navigation animations</li>
            </ul>

            <h3 class="text-xl font-semibold mb-2">Loading Indicators</h3>
            <ul class="list-disc ml-6 space-y-1 mb-4">
                <li>Page load animations</li>
                <li>Data-fetching spinners</li>
                <li>Splash screen effects</li>
                <li>Network status indicators</li>
            </ul>

            <h3 class="text-xl font-semibold mb-2">Onboarding Screens</h3>
            <ul class="list-disc ml-6 space-y-1 mb-4">
                <li>Step-by-step product tutorials</li>
                <li>Animated tooltips or hints</li>
                <li>User success illustrations</li>
                <li>Explainer sequences</li>
            </ul>

            <h3 class="text-xl font-semibold mb-2">Marketing Content</h3>
            <ul class="list-disc ml-6 space-y-1 mb-4">
                <li>Hero banners</li>
                <li>Social media snippets</li>
                <li>Product landing pages</li>
                <li>Email headers or in-app promotions</li>
            </ul>
        </div>

        <div>
            <h2 class="text-2xl font-bold mb-4">Compatibility and Platforms</h2>
            <h3 class="text-xl font-semibold mb-2">Lottie Preview for iOS</h3>
            <ul class="list-disc ml-6 space-y-1 mb-4">
                <li>Simulate how animations appear in iOS apps</li>
                <li>Test responsiveness and screen scaling in real time</li>
                <li>Ensure visual consistency with Apple design guidelines</li>
                <li>Avoid app-store delays by catching issues early</li>
            </ul>

            <h3 class="text-xl font-semibold mb-2">Mac Support</h3>
            <ul class="list-disc ml-6 space-y-1 mb-4">
                <li>Works on Safari, Chrome, and Firefox for Mac</li>
                <li>Optimized for Retina displays</li>
                <li>Seamless integration with macOS creative tools</li>
            </ul>

            <h3 class="text-xl font-semibold mb-2">Browser Compatibility</h3>
            <p class="mb-4">Supported browsers:</p>
            <ul class="list-disc ml-6 space-y-1 mb-4">
                <li>Google Chrome</li>
                <li>Mozilla Firefox</li>
                <li>Apple Safari</li>
                <li>Microsoft Edge</li>
                <li>Chromium-based browsers (Brave, Opera)</li>
            </ul>

            <h3 class="text-xl font-semibold mb-2">Integrations with Tools</h3>
            <p class="mb-2 font-semibold">🔄 After Effects (via Bodymovin)</p>
            <ul class="list-disc ml-6 space-y-1 mb-4">
                <li>Export animations with Bodymovin</li>
                <li>Upload JSON into Lottie Preview</li>
                <li>Preview and customize in real time</li>
            </ul>
            <p class="mb-2 font-semibold">🌐 LottieFiles Compatibility</p>
            <ul class="list-disc ml-6 space-y-1 mb-4">
                <li>Supports same JSON format as LottieFiles</li>
                <li>Preview LottieFiles animations easily</li>
                <li>Alternative to cloud-based editors</li>
            </ul>
        </div>

        <div>
            <h2 class="text-2xl font-bold mb-4">Comparison with Other Tools</h2>
            <p class="mb-4">Here’s how Lottie Preview compares to LottieFiles:</p>
            <table class="w-full border text-sm mb-4">
                <thead>
                    <tr class="bg-gray-100 text-left">
                        <th class="border px-4 py-2">Feature</th>
                        <th class="border px-4 py-2">Lottie Preview</th>
                        <th class="border px-4 py-2">LottieFiles</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="border px-4 py-2">Free to use</td>
                        <td class="border px-4 py-2">✅ Yes, fully free</td>
                        <td class="border px-4 py-2">✅ Yes, with paid tiers</td>
                    </tr>
                    <tr>
                        <td class="border px-4 py-2">Login required</td>
                        <td class="border px-4 py-2">❌ No login</td>
                        <td class="border px-4 py-2">✅ Required</td>
                    </tr>
                    <tr>
                        <td class="border px-4 py-2">Preview Lottie JSON</td>
                        <td class="border px-4 py-2">✅ Instant</td>
                        <td class="border px-4 py-2">✅ Supported</td>
                    </tr>
                    <tr>
                        <td class="border px-4 py-2">Zoom support</td>
                        <td class="border px-4 py-2">✅ Built-in</td>
                        <td class="border px-4 py-2">❌ Not available</td>
                    </tr>
                    <tr>
                        <td class="border px-4 py-2">Export MP4/GIF</td>
                        <td class="border px-4 py-2">✅ Planned</td>
                        <td class="border px-4 py-2">✅ Premium</td>
                    </tr>
                    <tr>
                        <td class="border px-4 py-2">Performance</td>
                        <td class="border px-4 py-2">✅ Lightweight</td>
                        <td class="border px-4 py-2">⚠️ Slower on large files</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</section>
