{{-- <x-app-layout>
    <!-- Hero Section -->
    <div class="hero">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="hero-content text-center">
                <h1 class="animate-fade-in">Preview Lottie Animation Instantly</h1>
                <p class="animate-slide-up">Drag & drop your Lottie JSON files to preview them instantly with our Lottie player.</p>
            </div>
        </div>
    </div>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    @if (session('error'))
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                            {{ session('error') }}
                        </div>
                    @endif

                    <!-- Upload Area -->
                    <div id="drop-area" class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                        <div class="space-y-4">
                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <div class="text-gray-600">
                                <p class="text-lg font-medium">Drag & drop your Lottie JSON file here</p>
                                <p class="text-sm">or</p>
                                <button id="browse-button" class="mt-2 btn-primary px-4 py-2 rounded-md text-sm">Browse Files</button>
                            </div>
                            <input type="file" id="lottie_file" class="hidden" accept=".json">
                        </div>
                    </div>

                    <!-- Preview Container -->
                    <div id="preview-container" class="hidden">
                        <div class="bg-white rounded-md shadow-md p-6">
                            <div class="flex justify-between items-center mb-6">
                                <h3 class="text-lg font-semibold" id="file-name-display">Preview</h3>
                                <div class="flex items-center space-x-4">
                                   
                                    <button id="close-preview" class="text-gray-500 hover:text-gray-700">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20"
                                            fill="currentColor">
                                            <path fill-rule="evenodd"
                                                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                                clip-rule="evenodd" />
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            <div class="flex flex-col md:flex-row gap-8">
                                <!-- Lottie Preview -->
                                <div class="w-full md:w-2/3">
                                    <div id="lottie-container" class="bg-gray-100 rounded-lg p-4 flex items-center justify-center" style="min-height: 400px;">
                                        <lottie-player id="lottiePlayer" background="transparent" speed="1" style="width: 100%; height: 100%;" loop autoplay></lottie-player>
                                    </div>

                                    <!-- Playback Controls -->
                                    <div class="mt-4 flex flex-wrap items-center gap-2">
                                        <button id="play" class="btn-primary px-3 py-1 rounded-md text-sm">Play</button>
                                        <button id="pause" class="bg-gray-200 text-gray-700 px-3 py-1 rounded-md text-sm">Pause</button>
                                        <button id="stop" class="bg-gray-200 text-gray-700 px-3 py-1 rounded-md text-sm">Stop</button>
                                        <div class="flex items-center gap-2 ml-4">
                                            <button class="speed-btn bg-gray-200 text-gray-700 px-2 py-1 rounded text-sm" data-speed="0.25">0.25x</button>
                                            <button class="speed-btn bg-gray-200 text-gray-700 px-2 py-1 rounded text-sm" data-speed="0.5">0.5x</button>
                                            <button class="speed-btn bg-gray-700 text-white px-2 py-1 rounded text-sm" data-speed="1">1x</button>
                                            <button class="speed-btn bg-gray-200 text-gray-700 px-2 py-1 rounded text-sm" data-speed="1.5">1.5x</button>
                                            <button class="speed-btn bg-gray-200 text-gray-700 px-2 py-1 rounded text-sm" data-speed="2">2x</button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Controls -->
                                <div class="w-full md:w-1/3">
                                    <div class="bg-gray-50 rounded-lg p-6">
                                        <h3 class="text-lg font-semibold mb-4">Animation Controls</h3>

                                        <div class="space-y-4">
                                            <div>
                                                <label for="background" class="block text-sm font-medium text-gray-700 mb-1">Background Color</label>
                                                <input type="color" id="background" value="#f3f4f6" class="w-full h-10 p-1 rounded border border-gray-300">
                                            </div>
                                            <!-- Lottie Color Editor -->
                                            <div id="color-editor" class="mt-4">
                                                <label class="block text-sm font-medium text-gray-700 mb-1">Edit Lottie Colors</label>
                                                <div id="color-swatches" class="grid grid-cols-2 gap-2 pr-10"></div>
                                            </div>
                                            <div class="flex items-center">
                                                <input type="checkbox" id="loop" checked class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                                                <label for="loop" class="ml-2 block text-sm text-gray-700">Loop Animation</label>
                                            </div>
                                            <div class="flex items-center">
                                                <input type="checkbox" id="autoplay" checked class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                                                <label for="autoplay" class="ml-2 block text-sm text-gray-700">Autoplay</label>
                                            </div>
                                            <div class="pt-4">
                                                <button id="download-json" class="w-full btn-primary px-4 py-2 rounded-md text-sm">Download JSON</button>
                                            </div>
                                            @auth
                                                <div class="pt-4" id="server-upload-container">
                                                    <form id="server-upload-form" class="w-full">
                                                        <button type="submit" class="w-full bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm hover:bg-gray-300">🔗 Share Lottie</button>
                                                    </form>
                                                </div>
                                            @else
                                                <div class="pt-4">
                                                    <a href="{{ route('login') }}" class="w-full bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm hover:bg-gray-300 block text-center">Login to Save</a>
                                                </div>
                                            @endauth
                                            <div class="pt-4 hidden" id="share-email-container">
                                                <label for="share-email"
                                                    class="block text-sm font-medium text-gray-700 mb-1">Share via Email</label>
                                                <div class="flex gap-2">
                                                    <input type="email" id="share-email" placeholder="Enter email address" class="flex-1 rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                                                    <button id="share-button" class="btn-primary text-white px-4 py-2 rounded-md text-sm hover:bg-primary-dark"> Share </button>
                                                </div>
                                            </div>
                                            <div class="pt-4 hidden" id="share-link-container">
                                                <label for="share-link"
                                                    class="block text-sm font-medium text-gray-700 mb-1">Share via Link</label>
                                                <div class="flex gap-2">
                                                    <input type="text" id="share-link" readonly class="flex-1 rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                                                    <button id="copy-link" class="btn-primary text-white px-4 py-2 rounded-md text-sm hover:bg-primary-dark"> Copy</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Features Section -->
    <div class="features bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold">Why Use Lottie Animations?</h2>
                <p class="mt-4 text-lg text-gray-600">Lottie animations offer numerous advantages over traditional
                    animation formats</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="card feature-card">
                    <div class="feature-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Lightweight</h3>
                    <p class="text-gray-600">Lottie files are significantly smaller than GIFs or videos, resulting in
                        faster loading times.</p>
                </div>

                <div class="card feature-card">
                    <div class="feature-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Scalable</h3>
                    <p class="text-gray-600">Lottie animations are vector-based, so they scale perfectly to any size
                        without losing quality.</p>
                </div>

                <div class="card feature-card">
                    <div class="feature-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Interactive</h3>
                    <p class="text-gray-600">Lottie animations can be interactive, allowing users to control playback
                        and interact with the animation.</p>
                </div>
            </div>
        </div>
    </div>

</x-app-layout> --}}
