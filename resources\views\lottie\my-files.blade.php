<x-app-layout>
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Header Section -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">My Lottie Animations</h1>
                <p class="mt-2 text-gray-600">View and manage your Lottie animations</p>
            </div>

            <!-- Filters and Search -->
            <div class="bg-white rounded-lg shadow-sm p-4 mb-6">
                <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
                    <!-- Search -->
                    <div class="relative w-full sm:w-96">
                        <input type="text" id="search" placeholder="Search animations"
                            class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-primary focus:border-primary">
                        {{-- <svg class="absolute left-3 top-2.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg> --}}
                    </div>

                    <!-- Sort Dropdown -->
                    <div class="flex items-center gap-4 w-full sm:w-auto">
                        <select id="sort-select"
                            class="border border-gray-300 rounded-lg mx-5 py-2 focus:ring-primary focus:border-primary">
                            <option value="newest">Newest First</option>
                            <option value="oldest">Oldest First</option>
                            <option value="name">Name</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Grid of Lottie Files -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" id="lottie-grid">
                <!-- Loading State -->
                <div id="loading-state" class="col-span-full flex justify-center py-12">
                    <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                </div>

                <!-- No Files State -->
                <div id="no-files-state" class="hidden col-span-full">
                    <div class="text-center py-12">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No animations</h3>
                        <p class="mt-1 text-sm text-gray-500">Get started by creating a new animation</p>
                        <div class="mt-6">
                            <a href="{{ route('lottie.index') }}" class="btn-primary px-4 py-2 text-sm rounded-md">
                                Upload Lottie
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Template for Lottie Cards (Will be populated by JS) -->
                <template id="lottie-card-template">
                    <div
                        class="bg-white rounded-lg shadow-sm overflow-hidden group hover:shadow-md transition-shadow duration-200">
                        <!-- Preview Area -->
                        <div class="relative aspect-video bg-gray-50 flex items-center justify-center overflow-hidden">
                            <lottie-player class="w-full h-full" background="transparent" speed="1" loop>
                            </lottie-player>

                            <!-- Overlay with actions -->
                            <div
                                class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center gap-2">
                                <a href="#"
                                    class="preview-btn bg-white text-gray-800 p-2 rounded-full hover:bg-gray-100">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                    </svg>
                                </a>
                                <button class="share-btn bg-white text-gray-800 p-2 rounded-full hover:bg-gray-100">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <!-- Info Area -->
                        <div class="p-4">
                            <div class="flex justify-between items-start">
                                <div class="flex-1 min-w-0">
                                    <h3 class="text-sm font-medium text-gray-900 truncate animation-name"></h3>
                                    <p class="mt-1 text-xs text-gray-500 created-at"></p>
                                </div> <!-- Actions Dropdown -->
                                <div class="relative ml-4">
                                    <button class="text-gray-400 hover:text-gray-600 action-menu-button">
                                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                            <path
                                                d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
                                        </svg>
                                    </button>
                                    <!-- Dropdown Menu -->
                                    <div
                                        class="action-menu hidden absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50">
                                        <div class="py-1" role="menu">
                                            <button
                                                class="preview-action block w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 text-left"
                                                role="menuitem">
                                                Preview
                                            </button>
                                            <button
                                                class="copy-link-action block w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 text-left"
                                                role="menuitem">
                                                Copy Page Link
                                            </button>
                                            <button
                                                class="rename-action block w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 text-left"
                                                role="menuitem">
                                                Rename
                                            </button>
                                            <button
                                                class="download-action block w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 text-left"
                                                role="menuitem">
                                                Download JSON
                                            </button>
                                            <button
                                                class="download-gif-action block w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 text-left"
                                                role="menuitem">
                                                Download GIF
                                            </button>
                                            <button
                                                class="download-mov-action block w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 text-left"
                                                role="menuitem">
                                                Download MOV
                                            </button>
                                            <button
                                                class="download-webm-action block w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 text-left"
                                                role="menuitem">
                                                Download WebM
                                            </button>
                                            <button
                                                class="delete-action block w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 text-left"
                                                role="menuitem">
                                                Delete Asset
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const grid = document.getElementById('lottie-grid');
                const template = document.getElementById('lottie-card-template');
                const loadingState = document.getElementById('loading-state');
                const noFilesState = document.getElementById('no-files-state');
                const searchInput = document.getElementById('search');
                const sortSelect = document.getElementById('sort-select');

                let allFiles = [];
                let filteredFiles = [];
                let currentSearchTerm = '';

                // Fetch Lottie files from the API
                async function fetchLottieFiles() {
                    console.log('Fetching files...');
                    try {
                        const response = await fetch('{{ route('lottie.user.files') }}', {
                            headers: {
                                'Accept': 'application/json',
                                'X-CSRF-TOKEN': window.csrfToken
                            },
                            credentials: 'same-origin'
                        });

                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }

                        const data = await response.json();

                        if (data.success) {
                            allFiles = data.data;
                            filteredFiles = [...allFiles];

                            // Apply initial sort
                            sortFiles();
                        }
                    } catch (error) {
                        console.error('Error fetching files:', error);
                    } finally {
                        loadingState.style.display = 'none';
                    }
                }

                // Render files to the grid
                function renderFiles() {
                    // Clear existing cards (except states)
                    const existingCards = grid.querySelectorAll('.lottie-card');
                    existingCards.forEach(card => card.remove());

                    if (filteredFiles.length === 0) {
                        noFilesState.classList.remove('hidden');
                        return;
                    }

                    noFilesState.classList.add('hidden');

                    filteredFiles.forEach(file => {
                        const card = template.content.cloneNode(true);
                        const container = card.querySelector('div');
                        container.classList.add('lottie-card');

                        // Set up the lottie player
                        const player = card.querySelector('lottie-player');
                        player.setAttribute('src', file.preview_url);

                        // Set file info
                        card.querySelector('.animation-name').textContent = file.name;
                        card.querySelector('.created-at').textContent = new Date(file.created_at)
                            .toLocaleDateString();

                        // Set up action buttons
                        const previewBtn = card.querySelector('.preview-btn');
                        previewBtn.href = file.share_url;
                        const shareBtn = card.querySelector('.share-btn');
                        const actionMenuBtn = card.querySelector('.action-menu-button');
                        const actionMenu = card.querySelector('.action-menu');

                        // Handle dropdown menu toggle
                        actionMenuBtn.addEventListener('click', (e) => {
                            e.stopPropagation();
                            console.log('Toggling menu...');
                            actionMenu.classList.toggle('hidden');
                        });

                        // Close dropdown when clicking outside
                        document.addEventListener('click', () => {
                            actionMenu.classList.add('hidden');
                        });

                        // Preview action
                        card.querySelector('.preview-action').addEventListener('click', () => {
                            window.open(file.share_url, '_blank');
                        });

                        // Copy Link action
                        card.querySelector('.copy-link-action').addEventListener('click', () => {
                            navigator.clipboard.writeText(file.share_url).then(() => {
                                alert('Link copied to clipboard!');
                            });
                            actionMenu.classList.add('hidden');
                        });

                        // Rename action
                        card.querySelector('.rename-action').addEventListener('click', () => {
                            const newName = prompt('Enter new name:', file.name);
                            if (newName && newName !== file.name) {
                                fetch(`/lottie/${file.uuid}/rename`, {
                                        method: 'PUT',
                                        headers: {
                                            'Content-Type': 'application/json',
                                            'X-CSRF-TOKEN': window.csrfToken
                                        },
                                        body: JSON.stringify({
                                            name: newName
                                        })
                                    })
                                    .then(response => response.json())
                                    .then(data => {
                                        if (data.success) {
                                            fetchLottieFiles(); // Refresh the list
                                        }
                                    });
                            }
                            actionMenu.classList.add('hidden');
                        });                        // Download actions
                        card.querySelector('.download-action').addEventListener('click', () => {
                            window.location.href = `/lottie/${file.uuid}/download`;
                            actionMenu.classList.add('hidden');
                        });

                        card.querySelector('.download-gif-action').addEventListener('click', () => {
                            window.location.href = `/lottie/${file.uuid}/download/gif`;
                            actionMenu.classList.add('hidden');
                        });

                        card.querySelector('.download-mov-action').addEventListener('click', () => {
                            window.location.href = `/lottie/${file.uuid}/download/mov`;
                            actionMenu.classList.add('hidden');
                        });

                        card.querySelector('.download-webm-action').addEventListener('click', () => {
                            window.location.href = `/lottie/${file.uuid}/download/webm`;
                            actionMenu.classList.add('hidden');
                        });

                        // Delete action
                        card.querySelector('.delete-action').addEventListener('click', () => {
                            if (confirm(
                                    'Are you sure you want to delete this animation? This action cannot be undone.'
                                    )) {
                                fetch(`/lottie/${file.uuid}`, {
                                        method: 'DELETE',
                                        headers: {
                                            'X-CSRF-TOKEN': window.csrfToken
                                        }
                                    })
                                    .then(response => response.json())
                                    .then(data => {
                                        if (data.success) {
                                            fetchLottieFiles(); // Refresh the list
                                        }
                                    });
                            }
                            actionMenu.classList.add('hidden');
                        });

                        grid.appendChild(card);
                    });
                } // Sort functionality
                function sortFiles() {
                    const sortValue = sortSelect.value;

                    filteredFiles.sort((a, b) => {
                        switch (sortValue) {
                            case 'newest':
                                return new Date(b.created_at) - new Date(a.created_at);
                            case 'oldest':
                                return new Date(a.created_at) - new Date(b.created_at);
                            case 'name':
                                return a.name.localeCompare(b.name);
                            default:
                                return 0;
                        }
                    });

                    renderFiles();
                }

                // Search functionality
                searchInput.addEventListener('input', (e) => {
                    currentSearchTerm = e.target.value.toLowerCase();
                    filteredFiles = allFiles.filter(file =>
                        file.name.toLowerCase().includes(currentSearchTerm)
                    );
                    sortFiles(); // Apply current sort after filtering
                });

                // Sort event listener
                sortSelect.addEventListener('change', sortFiles);

                // Initial load
                fetchLottieFiles();
            });
        </script>
    @endpush
</x-app-layout>
