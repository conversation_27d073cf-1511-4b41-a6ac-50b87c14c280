<x-app-layout>
    <!-- Hero Section -->
    <div class="hero">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="hero-content text-center">
                <h1 class="animate-fade-in">Preview Lottie Animation Instantly</h1>
                <p class="animate-slide-up">Drag & drop your Lottie JSON files to preview them instantly with our Lottie player.</p>
                    <div class="py-12">
                        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                                <div class="p-6">
                                    @if (session('error'))
                                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                                            {{ session('error') }}
                                        </div>
                                    @endif

                                    <!-- Upload Area -->
                                    <div id="drop-area" class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                                        <div class="space-y-4">
                                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                            </svg>
                                            <div class="text-gray-600">
                                                <p class="text-lg font-medium">Drag & drop your Lottie JSON file here</p>
                                                <p class="text-sm">or</p>
                                                <button id="browse-button" class="mt-2 btn-primary px-4 py-2 rounded-md text-sm">Browse Files</button>
                                            </div>
                                            <input type="file" id="lottie_file" class="hidden" accept=".json">
                                        </div>
                                    </div>

                                    <!-- Preview Container -->
                                    <div id="preview-container" class="hidden">
                                        <div class="bg-white rounded-md shadow-md p-6">
                                            <div class="flex justify-between items-center mb-6">
                                                <h3 class="text-lg font-semibold" id="file-name-display">Preview</h3>
                                                <div class="flex items-center space-x-4">
                                                    {{-- <button id="share-button" class="text-gray-600 hover:text-primary" title="Copy share link">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                            <path d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z" />
                                                        </svg>
                                                    </button> --}}
                                                    <button id="close-preview" class="text-gray-500 hover:text-gray-700">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20"
                                                            fill="currentColor">
                                                            <path fill-rule="evenodd"
                                                                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                                                clip-rule="evenodd" />
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>

                                            <div class="flex flex-col md:flex-row gap-8">
                                                <!-- Lottie Preview -->
                                                <div class="w-full md:w-2/3">
                                                    <div id="lottie-container" class="bg-gray-100 rounded-lg p-4 flex items-center justify-center" style="min-height: 400px;">
                                                        <lottie-player id="lottiePlayer" background="transparent" speed="1" style="width: 100%; height: 100%;" loop autoplay></lottie-player>
                                                    </div>

                                                    <!-- Playback Controls -->
                                                    <div class="mt-4 flex flex-wrap items-center gap-2">
                                                        <button id="play" class="btn-primary px-3 py-1 rounded-md text-sm">Play</button>
                                                        <button id="pause" class="bg-gray-200 text-gray-700 px-3 py-1 rounded-md text-sm">Pause</button>
                                                        <button id="stop" class="bg-gray-200 text-gray-700 px-3 py-1 rounded-md text-sm">Stop</button>
                                                        <div class="flex items-center gap-2 ml-4">
                                                            <button class="speed-btn bg-gray-200 text-gray-700 px-2 py-1 rounded text-sm" data-speed="0.25">0.25x</button>
                                                            <button class="speed-btn bg-gray-200 text-gray-700 px-2 py-1 rounded text-sm" data-speed="0.5">0.5x</button>
                                                            <button class="speed-btn bg-gray-700 text-white px-2 py-1 rounded text-sm" data-speed="1">1x</button>
                                                            <button class="speed-btn bg-gray-200 text-gray-700 px-2 py-1 rounded text-sm" data-speed="1.5">1.5x</button>
                                                            <button class="speed-btn bg-gray-200 text-gray-700 px-2 py-1 rounded text-sm" data-speed="2">2x</button>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Controls -->
                                                <div class="w-full md:w-1/3">
                                                    <div class="bg-gray-50 rounded-lg p-6">
                                                        <h3 class="text-lg font-semibold mb-4">Animation Controls</h3>

                                                        <div class="space-y-4">
                                                            <div>
                                                                <label for="background" class="block text-sm font-medium text-gray-700 mb-1">Background Color</label>
                                                                <input type="color" id="background" value="#f3f4f6" class="w-full h-10 p-1 rounded border border-gray-300">
                                                            </div>
                                                            <!-- Lottie Color Editor -->
                                                            <div id="color-editor" class="mt-4">
                                                                <label class="block text-sm font-medium text-gray-700 mb-1">Edit Lottie Colors</label>
                                                                <div id="color-swatches" class="pr-10"></div>
                                                            </div>
                                                            <div class="flex items-center">
                                                                <input type="checkbox" id="loop" checked class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                                                                <label for="loop" class="ml-2 block text-sm text-gray-700">Loop Animation</label>
                                                            </div>
                                                            <div class="flex items-center">
                                                                <input type="checkbox" id="autoplay" checked class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                                                                <label for="autoplay" class="ml-2 block text-sm text-gray-700">Autoplay</label>
                                                            </div>
                                                            <div class="pt-4">
                                                                <button id="download-json" class="w-full btn-primary px-4 py-2 rounded-md text-sm">Download JSON</button>
                                                            </div>
                                                            @auth
                                                                <div class="pt-4" id="server-upload-container">
                                                                    <form id="server-upload-form" class="w-full">
                                                                        <button type="submit" class="w-full bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm hover:bg-gray-300">🔗 Share Lottie
                                                                            <span id="share-btn-spinner" class="spinner-border spinner-border-sm ms-2" style="display: none;" role="status" aria-hidden="true"></span>
                                                                        </button>
                                                                    </form>
                                                                </div>
                                                            @else
                                                                <div class="pt-4">
                                                                    <a href="{{ route('login') }}" class="w-full bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm hover:bg-gray-300 block text-center">Login to Save</a>
                                                                </div>
                                                            @endauth
                                                            <div class="pt-4 hidden" id="share-email-container">
                                                                <label for="share-email"
                                                                    class="block text-sm font-medium text-gray-700 mb-1">Share via Email</label>
                                                                <div class="flex gap-2">
                                                                    <input type="email" id="share-email" placeholder="Enter email address" class="flex-1 rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                                                                    <button id="share-button" class="btn-primary text-white px-4 py-2 rounded-md text-sm hover:bg-primary-dark"> Share </button>
                                                                </div>
                                                            </div>
                                                            <div class="pt-4 hidden" id="share-link-container">
                                                                <label for="share-link"
                                                                    class="block text-sm font-medium text-gray-700 mb-1">Share via Link</label>
                                                                <div class="flex gap-2">
                                                                    <input type="text" id="share-link" readonly class="flex-1 rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                                                                    <button id="copy-link" class="btn-primary text-white px-4 py-2 rounded-md text-sm hover:bg-primary-dark"> Copy </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
            </div>
        </div>
    </div>
    @include('lottie.description')
    <!-- Features Section -->
    <div class="features bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold">The world's largest free, ready-to-use, customizable animation library</h2>
                <p class="mt-4 text-lg text-gray-600">Discover thousands of free Lottie animations for your next project</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="card feature-card">
                    <div class="feature-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Lottie Creator</h3>
                    <p class="text-gray-600">A powerful web-based Lottie animation tool, designed to create ultra-lightweight animations.</p>
                </div>

                <div class="card feature-card">
                    <div class="feature-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Web Player</h3>
                    <p class="text-gray-600">Easily embed and control Lottie animations on your website with our powerful web player.</p>
                </div>

                <div class="card feature-card">
                    <div class="feature-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Mobile App</h3>
                    <p class="text-gray-600">Access your Lottie animations on the go with our mobile app for iOS and Android.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Section -->
    <div class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
                <div>
                    <div class="text-4xl font-bold text-primary">10x</div>
                    <p class="mt-2 text-gray-600">faster to ship</p>
                </div>
                <div>
                    <div class="text-4xl font-bold text-primary">5x</div>
                    <p class="mt-2 text-gray-600">faster page load speed</p>
                </div>
                <div>
                    <div class="text-4xl font-bold text-primary">45%</div>
                    <p class="mt-2 text-gray-600">increase in user engagement</p>
                </div>
                <div>
                    <div class="text-4xl font-bold text-primary">6x</div>
                    <p class="mt-2 text-gray-600">smaller than GIF</p>
                </div>
            </div>
        </div>
    </div>
    <!-- Subscribe for Feature Updates -->
<section class="max-w-xl mx-auto my-12 p-6 bg-white rounded-2xl shadow-md">
  <h2 class="text-2xl font-bold mb-4">Subscribe for Feature Updates</h2>
  <p class="mb-4">
    Want early access to upcoming features like:
  </p>
  <ul class="list-disc list-inside mb-4 space-y-1">
    <li>Exporting MP4 (WebM) and GIF</li>
    <li>Transparent GIF support</li>
    <li>Direct integration with After Effects</li>
  </ul>
  <p class="mb-6">
    📬 Subscribe to our newsletter and be the first to know when new features launch. We’ll only send helpful updates—no spam, ever.
  </p>
  <form class="space-y-4">
    <div>
      <label for="name" class="block font-medium">Name *</label>
      <input type="text" id="name" name="name" required class="w-full p-2 border border-gray-300 rounded-lg" />
    </div>
    <div>
      <label for="email" class="block font-medium">Email *</label>
      <input type="email" id="email" name="email" required class="w-full p-2 border border-gray-300 rounded-lg" />
    </div>
    <button type="submit" class="w-full py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
      Submit
    </button>
  </form>
</section>

<!-- Join the Community or Share Feedback -->
<section class="max-w-xl mx-auto my-12 p-6 bg-white rounded-2xl shadow-md">
  <h2 class="text-2xl font-bold mb-4">Join the Community or Share Feedback</h2>
  <p class="mb-4">
    We’re building Lottie Preview for real people—designers, developers, and animators who need a better way to work with Lottie animations. Your feedback helps us improve and grow.
  </p>
  <ul class="list-disc list-inside mb-4 space-y-1">
    <li>💬 Got suggestions?</li>
    <li>📢 Found a bug?</li>
    <li>🎯 Want to contribute or collaborate?</li>
  </ul>
  <p class="mb-6">Join the conversation:</p>
  <ul class="list-disc list-inside space-y-1 mb-4">
    <li>Drop us a message via our contact form</li>
    <li>Share your use case or testimonial</li>
    <li>Request a feature you’d love to see next</li>
  </ul>
  <p>
    Let’s shape the future of animation preview tools—together.
  </p>
</section>

</x-app-layout>
