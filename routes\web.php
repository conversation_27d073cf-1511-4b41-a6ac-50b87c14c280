<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\LottieController;

Route::view('/', 'welcome')->name('dashboard');

// Route::view('/', 'welcome')
//     ->middleware(['auth', 'verified'])
//     ->name('dashboard');

Route::view('profile', 'profile')
    ->middleware(['auth'])
    ->name('profile');

// Lottie Routes
Route::get('/previewlottie', [LottieController::class, 'index'])->name('lottie.index');
Route::post('/lottie/upload', [LottieController::class, 'upload'])->name('lottie.upload');
Route::get('/lottie/{uuid}', [LottieController::class, 'show'])->name('lottie.show');
Route::get('/lottie/preview/{uuid}', [LottieController::class, 'preview'])->name('lottie.preview');
Route::post('/lottie/{uuid}/share-email', [LottieController::class, 'shareViaEmail'])->name('lottie.share.email');
Route::put('/lottie/{uuid}/rename', [LottieController::class, 'rename'])->name('lottie.rename');
Route::delete('/lottie/{uuid}', [LottieController::class, 'delete'])->name('lottie.delete');
Route::get('/lottie/{uuid}/download/{format?}', [LottieController::class, 'download'])->name('lottie.download');
Route::get("/payment", function () {
    return view("payment.payment");
});
Route::get("/receipt", function () {
    return view("payment.receipt");
});
Route::get("/guest", function () {
    return view("landing.landing");
});

// User's Lottie Files
Route::get('/user/lottie-files', [LottieController::class, 'getUserLottieFiles'])
    ->middleware('auth')
    ->name('lottie.user.files');

Route::get('/my-files', [LottieController::class, 'myFiles'])
    ->middleware('auth')
    ->name('lottie.my.files');

require __DIR__.'/auth.php';
