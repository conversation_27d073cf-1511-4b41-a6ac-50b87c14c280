<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

        <title><?php echo e(config('app.name', 'ShowLottie')); ?></title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700,800" rel="stylesheet" />

        <!-- Scripts -->
        <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/css/lottie-theme.css', 'resources/js/app.js'
            , 'resources/js/common.js', 'resources/js/lottie-player.js']); ?>

        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
        <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>
        <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-auth.js"></script>
        <script type="module" src="<?php echo e(asset('firebase.js')); ?>"></script>
        <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::styles(); ?>

    </head>
    <body class="font-sans antialiased">
        <div class="min-h-screen bg-gray-50">
            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('layout.navigation', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-2584376539-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>

        <!-- CSRF Token for AJAX requests -->
        <script>
            window.csrfToken = '<?php echo e(csrf_token()); ?>';
        </script>

            <!-- Page Heading -->
            <?php if(isset($header)): ?>
                <header class="bg-white shadow">
                    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                        <?php echo e($header); ?>

                    </div>
                </header>
            <?php endif; ?>

            <!-- Page Content -->
            <main>
                <?php echo e($slot); ?>

            </main>

            <!-- Footer -->
            <footer class="footer">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                        <div class="footer-links">
                            <h4>ShowLottie</h4>
                            <p class="mt-2 text-sm text-gray-300">
                                Create, edit, collaborate on and implement lightweight Lottie animations across websites, apps, socials and more.
                            </p>
                        </div>

                        <div class="footer-links">
                            <h4>Products</h4>
                            <ul>
                                <li><a href="#">Free Animations</a></li>
                                <li><a href="#">Marketplace</a></li>
                                <li><a href="#">Lottie Editor</a></li>
                                <li><a href="#">Lottie Creator</a></li>
                            </ul>
                        </div>

                        <div class="footer-links">
                            <h4>Resources</h4>
                            <ul>
                                <li><a href="#">Blog</a></li>
                                <li><a href="#">What is Lottie</a></li>
                                <li><a href="#">Community</a></li>
                                <li><a href="#">Developer Portal</a></li>
                            </ul>
                        </div>

                        <div class="footer-links">
                            <h4>Company</h4>
                            <ul>
                                <li><a href="#">About Us</a></li>
                                <li><a href="#">Careers</a></li>
                                <li><a href="#">Contact</a></li>
                                <li><a href="#">Privacy Policy</a></li>
                            </ul>
                        </div>
                    </div>

                    <div class="footer-bottom">
                        <p>&copy; <?php echo e(date('Y')); ?> ShowLottie. All rights reserved.</p>
                    </div>
                </div>
            </footer>
        </div>

        <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::scripts(); ?>

        <?php echo $__env->yieldPushContent('scripts'); ?>
    </body>
</html>
<?php /**PATH E:\Web_Development\showLottie\resources\views/layouts/app.blade.php ENDPATH**/ ?>